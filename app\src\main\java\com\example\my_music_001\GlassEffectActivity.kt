package com.example.my_music_001

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import kotlin.math.roundToInt
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.example.my_music_001.ui.theme.My_music_001Theme
import android.graphics.Bitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.ComposeView
import android.view.View
import android.graphics.Canvas
import androidx.compose.ui.platform.LocalView

@Composable
fun GlassEffectMainScreen(
    onDismiss: (() -> Unit)? = null,
    mainBackgroundBitmap: Bitmap? = null, // 接收主页的背景图片
    backgroundContent: @Composable () -> Unit = {} // 新增：实时背景内容
) {
    var showBlurDialog by remember { mutableStateOf(false) }
    var dialogOffset by remember { mutableStateOf(Offset.Zero) }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景内容层 - 使用实时内容
        Box(modifier = Modifier.fillMaxSize()) {
            // 渲染实时背景内容
            backgroundContent()

            // 控制按钮层
            BackgroundControlLayer(
                onShowDialog = { showBlurDialog = true },
                onDismiss = onDismiss
            )
        }

        // 毛玻璃弹窗 - 使用实时模糊
        if (showBlurDialog) {
            LiveBlurDialog(
                backgroundContent = backgroundContent,
                onDismiss = { showBlurDialog = false },
                onOffsetChange = { dialogOffset = it }
            )
        }
    }
}

@Composable
fun BackgroundControlLayer(
    onShowDialog: () -> Unit,
    onDismiss: (() -> Unit)? = null
) {
    // 控制按钮层
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 右下角按钮
        Button(
            onClick = onShowDialog,
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(24.dp)
        ) {
            Text("模糊效果")
        }

        // 添加返回按钮（左上角）
        onDismiss?.let { dismiss ->
            Button(
                onClick = dismiss,
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(24.dp)
            ) {
                Text("返回")
            }
        }
    }
}

/**
 * 实时模糊对话框 - 直接使用背景内容进行实时模糊
 */
@Composable
fun LiveBlurDialog(
    backgroundContent: @Composable () -> Unit,
    onDismiss: () -> Unit,
    onOffsetChange: (Offset) -> Unit
) {
    // 拖动偏移状态
    var offset by remember { mutableStateOf(Offset.Zero) }

    // 获取屏幕尺寸
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidthPx = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeightPx = with(density) { configuration.screenHeightDp.dp.toPx() }

    // 通知父组件偏移变化
    LaunchedEffect(offset) {
        onOffsetChange(offset)
    }

    // 使用Box覆盖整个屏幕
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0f))
            .pointerInput(Unit) {
                // 点击空白区域关闭弹窗
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        // 毛玻璃效果的弹窗
        Box(
            modifier = Modifier
                .width(320.dp)
                .height(145.dp)
                .offset { IntOffset(offset.x.roundToInt(), offset.y.roundToInt()) }
                .align(Alignment.Center)
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offset += dragAmount
                    }
                }
        ) {
            // 实时背景模糊层
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(20.dp))
            ) {
                // 实时背景内容 - 根据弹窗位置进行偏移和模糊
                Box(
                    modifier = Modifier
                        .layout { measurable, constraints ->
                            // 使用真实的屏幕尺寸来渲染背景
                            val screenWidth = screenWidthPx.roundToInt()
                            val screenHeight = screenHeightPx.roundToInt()
                            val placeable = measurable.measure(
                                constraints.copy(
                                    minWidth = screenWidth,
                                    maxWidth = screenWidth,
                                    minHeight = screenHeight,
                                    maxHeight = screenHeight
                                )
                            )
                            layout(constraints.maxWidth, constraints.maxHeight) {
                                // 根据弹窗位置偏移背景，实现"透过弹窗看背景"的效果
                                placeable.place(
                                    -offset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2,
                                    -offset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2
                                )
                            }
                        }
                        .blur(80.dp) // 应用模糊效果
                        .graphicsLayer {
                            // 调整颜色和对比度
                            colorFilter = ColorFilter.colorMatrix(
                                ColorMatrix(
                                    floatArrayOf(
                                        1.4f, 0f, 0f, 0f, -0.4f,  // 红色通道：增强对比度，降低亮度
                                        0f, 1.4f, 0f, 0f, -0.4f,  // 绿色通道：增强对比度，降低亮度
                                        0f, 0f, 1.4f, 0f, -0.4f,  // 蓝色通道：增强对比度，降低亮度
                                        0f, 0f, 0f, 1f, 0f       // 透明度通道
                                    )
                                )
                            )
                        }
                ) {
                    // 渲染实时背景内容
                    backgroundContent()
                }

                // 半透明叠加层
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.White.copy(alpha = 0.15f))
                        .clip(RoundedCornerShape(20.dp))
                )

                // 弹窗内容
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "实时模糊效果",
                        color = Color.White,
                        style = MaterialTheme.typography.headlineSmall
                    )
                }
            }
        }
    }
}

/**
 * 保留原有的静态模糊对话框作为备用
 */
@Composable
fun RealTimeBlurDialog(
    mainBackgroundBitmap: Bitmap?,
    onDismiss: () -> Unit,
    onOffsetChange: (Offset) -> Unit
) {
    // 拖动偏移状态
    var offset by remember { mutableStateOf(Offset.Zero) }

    // 获取屏幕尺寸
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidthPx = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeightPx = with(density) { configuration.screenHeightDp.dp.toPx() }

    // 通知父组件偏移变化
    LaunchedEffect(offset) {
        onOffsetChange(offset)
    }

    // 使用Box覆盖整个屏幕
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0f))
            .pointerInput(Unit) {
                // 点击空白区域关闭弹窗
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        // 毛玻璃效果的正方形弹窗
        // 外部容器，处理拖拽和边框
        Box(
            modifier = Modifier
                .width(320.dp)  // 增加40dp（200 + 40）
                .height(145.dp) // 减少20dp（200 - 20）
                .offset { IntOffset(offset.x.roundToInt(), offset.y.roundToInt()) }
                .align(Alignment.Center)
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offset += dragAmount
                    }
                }
        ) {
            // 实时背景模糊层 - 重新渲染背景内容
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(20.dp))
            ) {
                // 背景图片 - 保持全屏尺寸，根据弹窗位置进行偏移
                mainBackgroundBitmap?.let { bitmap ->
                    Image(
                        bitmap = bitmap.asImageBitmap(),
                        contentDescription = "实时模糊背景",
                        colorFilter = ColorFilter.colorMatrix(
                            colorMatrix = ColorMatrix(
                                floatArrayOf(
                                    1.4f, 0f, 0f, 0f, -0.4f,  // 红色通道：保持对比度，但降低白色
                                    0f, 1.4f, 0f, 0f, -0.4f,  // 绿色通道：保持对比度，但降低白色
                                    0f, 0f, 1.4f, 0f, -0.4f,  // 蓝色通道：保持对比度，但降低白色
                                    0f, 0f, 0f, 1f, 0f       // 透明度通道
                                )
                            )
                        ),
                        modifier = Modifier
                            .layout { measurable, constraints ->
                                // 使用真实的屏幕尺寸
                                val screenWidth = screenWidthPx.roundToInt()
                                val screenHeight = screenHeightPx.roundToInt()
                                val placeable = measurable.measure(
                                    constraints.copy(
                                        minWidth = screenWidth,
                                        maxWidth = screenWidth,
                                        minHeight = screenHeight,
                                        maxHeight = screenHeight
                                    )
                                )
                                layout(constraints.maxWidth, constraints.maxHeight) {
                                    placeable.place(
                                        -offset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2,
                                        -offset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2
                                    )
                                }
                            }
                            .blur(80.dp), // 模糊效果
                        contentScale = ContentScale.Crop
                    )
                }

                // 半透明白色背景层（最上层）
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.White.copy(alpha = 0.15f))
                        .clip(RoundedCornerShape(20.dp))
                )
            }
        }
    }
}
